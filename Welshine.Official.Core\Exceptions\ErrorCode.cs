﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Text;

namespace Welshine.Official.Core.Exceptions
{
    public enum ErrorCode
    {
        #region 基础

        /// <summary>
        /// 序列化转换出错
        /// </summary>
        [Description("序列化转换出错")]
        SerializeFailed = -2,
        /// <summary>
        /// 参数格式错误-特性校验
        /// </summary>
        [Description("参数格式错误")]
        ValidationFailed = -1,
        /// <summary>
        /// None
        /// </summary>
        [Description("None")]
        None = 0,
        /// <summary>
        /// 鉴权失败
        /// </summary>
        [Description("鉴权失败")]
        Unauthorized = 401,

        /// <summary>
        /// 权限不足
        /// </summary>
        [Description("权限不足")]
        Forbidden = 403,

        /// <summary>
        /// 系统繁忙，请稍后再试
        /// </summary>
        [Description("系统繁忙，请稍后再试")]
        Error = 999,

        /// <summary>
        /// 参数错误
        /// </summary>
        [Description("参数错误")]
        Param = 1000,

        /// <summary>
        /// SN 参数错误
        /// </summary>
        [Description("业务域不存在")]
        FieldNotFind = 1001,

        /// <summary>
        /// 用户已存在
        /// </summary>
        [Description("用户已存在")]
        UserAlreadyExists = 1002,
        /// <summary>
        /// 用户不存在
        /// </summary>
        [Description("用户不存在")]
        UserNotFound = 1003,
        /// <summary>
        /// 系统错误
        /// </summary>
        [Description("系统错误")]
        SystemError = 1010,

        /// <summary>
        /// 没有数据变化
        /// </summary>
        [Description("没有数据变化.")]
        NoDataChange = 1011,
        /// <summary>
        /// 保存失败
        /// </summary>
        [Description("保存失败.")]
        SaveFail = 1012,
        /// <summary>
        /// 服务错误
        /// </summary>
        [Description("服务错误.")]
        ServiceFail = 1013,
        /// <summary>
        /// 提交数量超出限制
        /// </summary>
        [Description("提交数量超出限制.")]
        TimesLimit = 1014,
        /// <summary>
        /// 请勿提交无用的参数.
        /// </summary>
        [Description("请勿提交无用的参数.")]
        ParamUselessError = 1015,
        /// <summary>
        /// 文件Id不存在
        /// </summary>
        [Description("文件Id不存在.")]
        ParamFileIdError = 1016,
        /// <summary>
        /// 名称已存在
        /// </summary>
        [Description("名称已存在.")]
        NameExistError = 1017,
        /// <summary>
        /// 查询不到数据
        /// </summary>
        [Description("查询不到数据.")]
        NoDataWarn = 1018,
        /// <summary>
        /// 暂无数据导出
        /// </summary>
        [Description("暂无数据导出.")]
        NoDataExport = 1019,
        #endregion

        #region 业务

        /// <summary>
        /// 产品名称已存在
        /// </summary>
        [Description("产品名称已存在")]
        ProductNameError = 2001,

        /// <summary>
        /// 产品略缩图不存在
        /// </summary>
        [Description("产品略缩图不存在")]
        ProductThumbnailNotFind = 2002,

        /// <summary>
        /// 产品详情图，最多可上传5张图片
        /// </summary>
        [Description("产品详情图，最多可上传5张图片")]
        ProductDetailPictureLimitError = 2003,

        /// <summary>
        /// 产品详情图不存在
        /// </summary>
        [Description("产品详情图不存在")]
        ProductDetailPictureNotFind = 2004,

        /// <summary>
        /// 产品数量已达上限
        /// </summary>
        [Description("产品数量已达上限")]
        ProductCountError = 2005,

        /// <summary>
        /// 产品Id不存在
        /// </summary>
        [Description("产品Id不存在")]
        ProductNotFind = 2006,

        /// <summary>
        /// 展会名称已存在
        /// </summary>
        [Description("展会名称已存在")]
        ExhibitionNameError = 2007,

        /// <summary>
        /// 展会略缩图不存在
        /// </summary>
        [Description("展会略缩图不存在")]
        ExhibitionThumbnailNotFind = 2008,

        /// <summary>
        /// 展会详情图，最多可上传8张图片
        /// </summary>
        [Description("展会详情图，最多可上传8张图片")]
        ExhibitionDetailPictureLimitError = 2009,

        /// <summary>
        /// 展会详情图不存在
        /// </summary>
        [Description("展会详情图不存在")]
        ExhibitionDetailPictureNotFind = 2010,

        /// <summary>
        /// 展会Id不存在
        /// </summary>
        [Description("展会Id不存在")]
        ExhibitionNotFind = 2011,

        /// <summary>
        /// 图册名称已存在
        /// </summary>
        [Description("图册名称已存在")]
        AtlasNameError = 2012,

        /// <summary>
        /// 图册封面图不存在
        /// </summary>
        [Description("图册封面图不存在")]
        AtlasThumbnailNotFind = 2013,

        /// <summary>
        /// 图册文件不存在
        /// </summary>
        [Description("图册文件不存在")]
        AtlasFileNotFind = 2014,

        /// <summary>
        /// 图册Id不存在
        /// </summary>
        [Description("图册Id不存在")]
        AtlasNotFind = 2015,

        /// <summary>
        /// 已有门店显示首页
        /// </summary>
        [Description("已有门店显示首页")]
        StoreShowHomeError = 2201,
        /// <summary>
        /// 文件格式错误
        /// </summary>
        [Description("文件格式错误")]
        FileFormatError = 2202,
        /// <summary>
        /// 首页门店必须有联系图片
        /// </summary>
        [Description("首页门店必须有联系图片")]
        StoreRelateFileIdRequiredError = 2203,
        /// <summary>
        /// 门店不存在
        /// </summary>
        [Description("门店不存在")]
        StoreNoFoundError = 2204,

        /// <summary>
        /// 标题已存在
        /// </summary>
        [Description("标题已存在")]
        VersionsTitleError = 2205,

        /// <summary>
        /// 当前已有草稿或待审核的版本,暂时无法新增版本
        /// </summary>
        [Description("当前已有草稿或待审核的版本,暂时无法新增版本")]
        VersionsStatusError = 2206,

        /// <summary>
        /// 业务Id不存在
        /// </summary>
        [Description("业务Id不存在")]
        VersionsIdNoFoundError = 2207,

        /// <summary>
        /// 状态为已发布,暂时无法编辑
        /// </summary>
        [Description("状态为已发布,暂时无法编辑")]
        VersionsEditReleaseStatusrError = 2208,

        /// <summary>
        /// 审核状态不为待提交或审核驳回,暂时无法编辑
        /// </summary>
        [Description("审核状态不为待提交或审核驳回,暂时无法编辑")]
        VersionsEditApproverStatusError = 2209,

        /// <summary>
        /// 状态为已发布,暂时无法删除
        /// </summary>
        [Description("状态为已发布,暂时无法删除")]
        VersionsDeleteReleaseStatusError = 2210,

        /// <summary>
        /// 审核状态为待审核,暂时无法删除
        /// </summary>
        [Description("审核状态为待审核,暂时无法删除")]
        VersionsDeleteApproverStatusError = 2211,

        /// <summary>
        /// 版本已发布,暂时无法提交审核
        /// </summary>
        [Description("版本已发布,暂时无法提交审核")]
        VersionsSubmitReleaseStatusError = 2212,

        /// <summary>
        /// 版本已提交审核或已审核通过,暂时无法提交审核
        /// </summary>
        [Description("版本已提交审核或已审核通过,暂时无法提交审核")]
        VersionsSubmitApproverStatusError = 2213,

        /// <summary>
        /// 审核状态不为待审核,暂时无法审核
        /// </summary>
        [Description("审核状态不为待审核,暂时无法审核")]
        BannerVersionsApproveError = 2214,

        /// <summary>
        /// 必须至少有一个图片为开启状态
        /// </summary>
        [Description("必须至少有一个图片为开启状态")]
        VersionsImgEnableError = 2215,

        /// <summary>
        /// 版本已发布
        /// </summary>
        [Description("版本已发布")]
        VersionsReleaseStatusError = 2216,

        /// <summary>
        /// 版本审核状态不为审核通过,暂时无法发布
        /// </summary>
        [Description("版本审核状态不为审核通过,暂时无法发布")]
        VersionsApproverStatusError = 2217,

        /// <summary>
        /// 营业状态不一致,请重新编辑
        /// </summary>
        [Description("营业状态不一致,请重新编辑")]
        ContactsStatusError = 2218,

        /// <summary>
        /// 门店营业时间不一致,请重新编辑
        /// </summary>
        [Description("门店营业时间不一致,请重新编辑")]
        ContactsTimeError = 2219,

        /// <summary>
        /// 门店传真不一致,请重新编辑
        /// </summary>
        [Description("门店传真不一致,请重新编辑")]
        ContactsFaxError = 2220,

        /// <summary>
        /// 门店电话不一致,请重新编辑
        /// </summary>
        [Description("门店电话不一致,请重新编辑")]
        ContactsPhoneError = 2221,

        /// <summary>
        /// 分类名称(英文)已存在
        /// </summary>
        [Description("分类名称(英文)已存在")]
        CategoryNameENError = 2222,

        /// <summary>
        /// 上级分类不存在
        /// </summary>
        [Description("上级分类不存在")]
        CategoryParentNoFoundError = 2223,

        /// <summary>
        /// 不允许新增下级分类
        /// </summary>
        [Description("不允许新增下级分类")]
        CategoryLevelError = 2224,

        /// <summary>
        /// 分类不存在
        /// </summary>
        [Description("分类不存在")]
        CategoryNoFoundError = 2225,

        /// <summary>
        /// 商品图片重复
        /// </summary>
        [Description("商品图片重复")]
        CategoryProductImgDistinctError = 2226,

        /// <summary>
        /// 商品图片不存在
        /// </summary>
        [Description("商品图片不存在")]
        CategoryProductImgNoFontError = 2227,

        /// <summary>
        /// 分类产品不存在
        /// </summary>
        [Description("分类产品不存在")]
        CategoryProductNoFoundError = 2228,

        /// <summary>
        /// 产品已下架
        /// </summary>
        [Description("产品已下架")]
        CategoryProductReleaseStatusDowmError = 2229,

        /// <summary>
        /// 产品已发布
        /// </summary>
        [Description("产品已发布")]
        CategoryProductReleaseStatusUpError = 2230,

        /// <summary>
        /// 产品已发布,不允许编辑
        /// </summary>
        [Description("产品已发布,不允许编辑")]
        CategoryProductEditReleaseStatusError = 2231,

        /// <summary>
        /// 产品已发布,不允许删除
        /// </summary>
        [Description("产品已发布,不允许删除")]
        CategoryProductDeleteReleaseStatusError = 2232,

        /// <summary>
        /// 分类有下级,不允许删除
        /// </summary>
        [Description("分类有下级,不允许删除")]
        CategoryDeleteChilrenError = 2233,

        /// <summary>
        /// 分类下有未删除的商品,不允许删除
        /// </summary>
        [Description("分类下有未删除的商品,不允许删除")]
        CategoryDeleteProductError = 2234,

        /// <summary>
        /// 只允许二级分类新增产品
        /// </summary>
        [Description("只允许二级分类新增产品")]
        CategoryProductAddCategoryError = 2235,

        /// <summary>
        /// 当前用户与创建人不一致,不允许编辑
        /// </summary>
        [Description("当前用户与创建人不一致,不允许编辑")]
        VersionsEditUserError = 2236,

        /// <summary>
        /// 当前发布数量已达上限，请先下架后再操作
        /// </summary>
        [Description("当前发布数量已达上限，请先下架后再操作")]
        VersionsReleaseCountError = 2237,

        /// <summary>
        /// 版本未发布
        /// </summary>
        [Description("版本未发布")]
        VersionsReleaseStatusWError = 2238,

        /// <summary>
        /// 序号不允许重复
        /// </summary>
        [Description("序号不允许重复")]
        SidebarSortError = 2239,

        /// <summary>
        /// 二级分类底下的英文产品介绍数量限制999个
        /// </summary>
        [Description("二级分类底下的英文产品介绍数量限制999个")]
        CategoryProductAddCountError = 2240,

        /// <summary>
        /// 详情图至少上传2张
        /// </summary>
        [Description("详情图至少上传2张")]
        ProductCNImgCountError = 2241,

        /// <summary>
        /// 商品角图图片不存在
        /// </summary>
        [Description("商品角图图片不存在")]
        CategoryProductCornerMarkImgNoFontError = 2242,

        /// <summary>
        /// 分类名称(中文)已存在
        /// </summary>
        [Description("分类名称(中文)已存在")]
        CategoryNameCNError = 2243,

        /// <summary>
        /// 非一级分类不允许设置logo
        /// </summary>
        [Description("非一级分类不允许设置logo")]
        CategoryLogoError = 2244,

        /// <summary>
        /// 请上传logo图片
        /// </summary>
        [Description("请上传logo图片")]
        CategoryLogoNotUpError = 2245,

        /// <summary>
        /// 商品角图Id超出长度限制
        /// </summary>
        [Description("商品角图Id超出长度限制")]
        CategoryProductCornerMarkLengthError = 2246,

        /// <summary>
        /// 商品图片Id超出长度限制
        /// </summary>
        [Description("商品图片Id超出长度限制")]
        CategoryProductImgLengthError = 2247,

        /// <summary>
        /// 商品图片Id是必填项
        /// </summary>
        [Description("商品图片Id是必填项")]
        CategoryProductImgError = 2248,

        /// <summary>
        /// 商品角图Id是必填项
        /// </summary>
        [Description("商品角图Id是必填项")]
        CategoryProductCornerMarkError = 2249,

        /// <summary>
        /// 请上传手机版激活图标
        /// </summary>
        [Description("请上传手机版激活图标")]
        CategoryMobileLogoNotUpError = 2250,

        /// <summary>
        /// 非一级分类不允许设置手机版激活图标
        /// </summary>
        [Description("非一级分类不允许设置手机版激活图标")]
        CategoryMobileLogoError = 2251,

        /// <summary>
        /// 展会活动不存在
        /// </summary>
        [Description("展会活动不存在")]
        ExhibitionActivityNoFoundError = 2252,

        /// <summary>
        /// 展会活动未停用,不允许编辑
        /// </summary>
        [Description("展会活动未停用,不允许编辑")]
        ExhibitionActivityNotDisabledError = 2253,

        /// <summary>
        /// 当前活动非停用状态，无法启用
        /// </summary>
        [Description("当前活动非停用状态，无法启用")]
        ExhibitionActivityStartError = 2254,

        /// <summary>
        /// 当前活动非启用状态，无法停用
        /// </summary>
        [Description("当前活动非启用状态，无法停用")]
        ExhibitionActivityStopError = 2255,

        /// <summary>
        /// 当前活动非停用状态，无法删除
        /// </summary>
        [Description("当前活动非停用状态，无法删除")]
        DeleteExhibitionActivityStopError = 2256,

        /// <summary>
        /// 省份不存在
        /// </summary>
        [Description("省份不存在")]
        ProvinceNoFoundError = 2257,

        /// <summary>
        /// 市不存在
        /// </summary>
        [Description("市不存在")]
        CityNoFoundError = 2258,

        /// <summary>
        /// 活动已结束
        /// </summary>
        [Description("活动已结束")]
        ExhibitionActivityEndError = 2259,

        #endregion

        #region App
        /// <summary>
        /// 获取OpenId失败
        /// </summary>
        [Description("获取OpenId失败")]
        OpenIdError = 3001,
        /// <summary>
        /// 获取手机失败
        /// </summary>
        [Description("获取手机失败")]
        GetWxPhoneError = 3002,
        /// <summary>
        /// 图册编码失败
        /// </summary>
        [Description("获取图册编码失败")]
        GetAtlasError = 3003,
        /// <summary>
        /// 请授权手机号后在下载！
        /// </summary>
        [Description("请授权手机号后在下载！")]
        AuthPhoneError = 3004,
        /// <summary>
        /// 每天登记次数超出！
        /// </summary>
        [Description("每天登记次数超出！")]
        UserCheckInLimit = 3005,
        #endregion

        #region 组织用户管理
        [Description("部门超过上限99")]
        DepartmentCountOverLoad = 4001,
        [Description("部门/岗位不允许重名，请重新命名")]
        DepartmentPostNameRepeat =4002,
        [Description("部门下人员数为零才允许删除")]
        DepartmentDeleteError = 4003,
        [Description("查无此部门")]
        DepartmentNotExist =4004,
        [Description("岗位超过上限99")]
        PostCountOverLoad =4005,
        [Description("岗位下人员数为零才允许删除")]
        PostDeleteError =4006,
        [Description("查无此岗位")]
        PostNotExist = 4007,
        [Description("部门与岗位不匹配")]
        DepartmentPostNotMatch =4008,
        [Description("账号重复")]
        LoginNameRepeat =4009,
        [Description("姓名重复")]
        UserNameRepeat = 4010,
        [Description("电话重复")]
        TelRepeat = 4011,
        [Description("用户不存在")]
        AccountNotExist = 4012,
        [Description("用户已经被删除")]
        AccountDeleted = 4013,
        [Description("不允许更改自己的启用禁用状态")]
        ChangeStatusError = 4014,
        [Description("账号密码错误")]
        AccountLoginError = 4015,
        [Description("账号被停用")]
        AccountStatusError = 4016,
        #endregion

        #region 权限管理
        [Description("权限分组超过上限99")]
        GroupCountLimitError = 5001,
        [Description("权限分组不存在")]
        GroupNotExist = 5002,
        [Description("权限分组名称已存在")]
        GroupNameExist = 5003,
        [Description("权限分组内仍然存在用户，不允许修改组名")]
        GroupUserNotEmpty = 5004,
        [Description("传入了不存在的用户id")]
        GroupUserError = 5005,
        [Description("不能添加组中已经存在的用户id")]
        GroupUserAddError = 5006,
        [Description("不能移除组中不存在的用户id")]
        GroupUserRemoveError = 5007,
        [Description("非法的url")]
        RBACUrlListError =5008,

        #endregion

        #region 企业资讯
        [Description("企业资讯不存在")]
        CorporateNewNotExist = 6001,
        [Description("无法操作已发布的资讯")]
        CorporateNewStatusError = 6002,
        [Description("最多只能发布20条资讯")]
        CorporateNewReleaseError = 6003,
        #endregion
    }
}
