﻿using Microsoft.AspNetCore.Mvc;
using Serilog;
using System.Threading.Tasks;
using Welshine.Official.Core.Exceptions;
using Welshine.Official.Core.Extensions;
using Welshine.Official.Core.RestfulApi.Base;
using Welshine.Official.Domain.VO.App.Request;
using Welshine.Official.Domain.VO.App.Response;
using Welshine.Official.Service.Interface;
using Welshine.Official.WxApp.Api.Core;

namespace Welshine.Official.WxApp.Api.Controllers
{
    /// <summary>
    /// 用户
    /// </summary>
    public class UserController : BaseApiController
    {
        IUserService _userService;
        IWxService _wxService;
        /// <summary>
        /// 
        /// </summary>
        /// <param name="userService"></param>
        public UserController(IUserService userService, IWxService wxService)
        {
            _userService = userService;
            _wxService = wxService;
        }
        /// <summary>
        /// 微信  的小程序用户在获取OpenId，如果不存在就插入
        /// </summary>
        /// <response code="3001">获取OpenId失败</response>
        /// <param name="req">req</param>
        /// <returns></returns>
        [HttpPost]
      
        public async Task<BaseResponse<GetOpenIdResponse>> GetOrAddOpenId([FromBody] BaseRequest<OpenIdUserRequest> req)
        {
            GetOpenIdResponse result = null;
            try
            {
                var openId = await _wxService.GetOpenId(req.Body.Code);
                if (string.IsNullOrWhiteSpace(openId))
                {
                    return Failure(ErrorCode.OpenIdError, result);
                }
                result = await _userService.GetOrAddOpenId(openId);
                return Success("操作成功", result);
            }
            catch (BusinessException ex)
            {
                return Failure(ex.Code, ex.Message, result);
            }
            catch (System.Exception ex)
            {
                Log.Error("GetOpenId " + ex.Message + ex.StackTrace);
                return Failure(ErrorCode.ServiceFail.ToDescriptionName(), result);
            }
        }
        /// <summary>
        /// 微信小程序授权手机号码 新版本 基础库 2.21.2
        /// https://developers.weixin.qq.com/miniprogram/dev/framework/open-ability/getPhoneNumber.html
        /// code换取用户手机号,更新用户表手机号码
        /// </summary>
        /// <response code="3002">获取手机失败</response>
        /// <response code="1003">用户不存在</response>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<BaseResponse<GetOpenIdResponse>> WxAuthPhone([FromBody] BaseRequest<WxAuthPhoneRequet> request)
        {
            GetOpenIdResponse result = null;
            try
            {
                var phone = await _wxService.GetWxPhone(request.Body.Code);
                if (string.IsNullOrWhiteSpace(phone))
                {
                    return Failure(ErrorCode.GetWxPhoneError, result);
                }
                //更新用户手机号码
                await _userService.UpdateUserPhone(request.Body.OpenId, phone);
                result = new GetOpenIdResponse()
                {
                   OpenId= request.Body.OpenId,
                   Phone= phone
                };
                return Success("操作成功", result);
            }
            catch (BusinessException ex)
            {
                return Failure(ex.Code, ex.Message, result);
            }
            catch (System.Exception ex)
            {
                Log.Error("WxAuthPhone ex:" + ex.Message + ex.StackTrace);
                return Failure(ErrorCode.ServiceFail, result);
            }
        }
        /// <summary>
        /// 用户登记下载
        /// </summary>
        /// <response code="3003">图册编码错误</response>
        /// <response code="1003">OpenId不存在</response>
        /// <response code="3004">手机没有授权</response>
        /// <response code="3005">登记次数超出限制</response>
        /// <returns></returns>
        [HttpPost]
        public async Task<BaseResponse<bool>> UserCheckIn([FromBody] BaseRequest<CheckInRequest> request)
        {
            bool result = false;
            try
            {
                result = await _userService.UserCheckIn(request.Body.OpenId, request.Body.AtlasCode);
                return Success("操作成功", result);
            }
            catch (BusinessException ex)
            {
                return Failure(ex.Code, ex.Message, result);
            }
            catch (System.Exception ex)
            {
                Log.Error("UserCheckIn " + ex.Message + ex.StackTrace);
                return Failure(ErrorCode.ServiceFail.ToDescriptionName(), result);
            }
        }
    }
}
